import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, roc_auc_score
import joblib

# Carregar os dados de resíduos e rótulos
data = pd.read_csv('residuals_and_labels_falha8.csv')
print("Dados carregados de 'residuals_and_labels_falha8.csv':")
print(data.head())

print(data.shape)

# Preparar features (resíduos), alvo (rótulos) 
X_1 = data[['residuals_h1']].values
X_2 = data[['residuals_h2']].values
y = data[['labels_h1', 'labels_h2']].values  

print(X.shape)
print(y.shape)



data.head()

from sklearn.svm import SVC


# Dividir os dados em treino e teste 
X_train, X_test, y_train, y_test = train_test_split(
    X_1, X_2, y, test_size=0.33, random_state=42, stratify=y.ravel()
)
""""
# Normalizar os resíduos
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)
"""
# Salvar o scaler para uso futuro
#joblib.dump(scaler, 'scaler_classifier_falha8.joblib')
#print("Scaler salvo como 'scaler_classifier_falha8.joblib'")

# Treinar o classificador (Random Forest)
classifier = SVC(kernel='linear', random_state=42, class_weight='balanced', probability=True)
classifier.fit(X_train, y_train)

# Fazer previsões no conjunto de teste
y_pred = classifier.predict(X_test)

# Calcular métricas
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_pred)

print("\nMétricas do Classificador:")
print(f"Acurácia: {accuracy:.4f}")
print(f"Precisão: {precision:.4f}")
print(f"Recall: {recall:.4f}")
print(f"F1-Score: {f1:.4f}")
print(f"ROC-AUC: {roc_auc:.4f}")




# Gerar e plotar a matriz de confusão
cm = confusion_matrix(y_test, y_pred)
plt.figure(figsize=(8, 6))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=['Normal (0)', 'Falha (1)'], yticklabels=['Normal (0)', 'Falha (1)'])
plt.title('Matriz de Confusão - Classificador para Falha 8')
plt.xlabel('Previsto')
plt.ylabel('Real')
plt.show()



# Salvar o modelo treinado
#joblib.dump(classifier, 'classifier_falha8.joblib')
#print("Classificador salvo como 'classifier_falha8.joblib'")